<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太⼀企业管理有限公司</title>
    <!-- favicons Icons -->




    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">

    <!-- 自定义样式 -->
    <style>
        /* 默认状态：灰色背景，灰色图标 */
        .interaction-item .interaction-icon {
            background-color: #f8f9fa !important;
            color: #666 !important;
            transition: all 0.3s ease;
        }

        /* 点赞激活状态：黑色背景，白色图标 */
        .interaction-item.liked .interaction-icon {
            background-color: #333 !important;
            color: white !important;
        }

        /* 收藏激活状态：黑色背景，白色图标 */
        .interaction-item.favorited .interaction-icon {
            background-color: #333 !important;
            color: white !important;
        }

        /* 登录弹框样式 */
        .login-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-popup.active {
            visibility: visible;
            opacity: 1;
        }

        .login-popup .search-popup__content {
            background: #fff;
            border-radius: 8px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .login-popup__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .login-popup__header h3 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }

        .login-popup__close {
            background: none;
            border: none;
            font-size: 20px;
            color: #999;
            cursor: pointer;
            padding: 5px;
        }

        .login-popup__close:hover {
            color: #333;
        }

        .login-popup__form {
            display: flex;
            flex-direction: column;
        }

        .login-form-row {
            margin-bottom: 20px;
        }

        .login-form-row input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .login-form-row input:focus {
            outline: none;
            border-color: #ff6600;
        }

        .login-sms-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .login-sms-input {
            flex: 1;
        }

        .login-submit-btn {
            width: 100%;
            padding: 12px;
            background-color: #ff6600;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .login-submit-btn:hover {
            background-color: #e55a00;
        }

        .login-submit-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->

    <div id="app">
        <div class="page-wrapper">
            <header class="main-header clearfix">
                <div class="main-header__top">
                    <div class="container">
                        <div class="main-header__top-inner">
                            <div class="main-header__top-address">
                                <ul class="list-unstyled main-header__top-address-list">
                                    <li>
                                        <i class="icon">
                                            <span class="icon-pin"></span>
                                        </i>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon">
                                            <span class="icon-email"></span>
                                        </i>
                                        <div class="text">
                                            <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <nav class="main-menu clearfix">
                    <div class="main-menu__wrapper clearfix">
                        <div class="container">
                            <div class="main-menu__wrapper-inner clearfix">
                                <div class="main-menu__left">
                                    <div class="main-menu__logo">
                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 27px"></a>
                                    </div>
                                    <div class="main-menu__main-menu-box">
                                        <div class="main-menu__main-menu-box-inner">
                                            <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i
                                                    class="fa fa-bars"></i></a>
                                            <ul class="main-menu__list one-page-scroll-menu">
                                                <li class=" megamenu scrollToLink">
                                                    <a href="index-one-page.html">首页 </a>
                                                </li>

                                                <li class="scrollToLink ">
                                                    <a href="company.html">保险公司 </a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="products.html">保险产品</a>
                                                </li>
                                                <li class="scrollToLink current">
                                                    <a href="news.html">新闻资讯</a>
                                                </li>
                                                <li class="scrollToLink" style="margin-right: 37px;">
                                                    <a href="about.html">关于我们</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <div class="stricky-header stricked-menu main-menu">
                <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
            </div><!-- /.stricky-header -->



            <!--Page Header Start-->
            <section class="page-header">
                <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
                </div>
                <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
                <div class="container">
                    <div class="page-header__inner">
                        <!-- <ul class="thm-breadcrumb list-unstyled">
                        <li><a>Home</a></li>
                        <li><span>/</span></li>
                        <li>news</li>
                    </ul> -->
                        <h2>资讯详情</h2>
                    </div>
                </div>
            </section>
            <!--Page Header End-->

            <!--News One Start-->
            <section class="news-details">
                <div class="container">
                    <ul class="list-unstyled news-details__meta">
                        <li v-if="newsDetail.newsTime"><a href="#"><i class="far fa-calendar"></i>{{newsDetail.newsTime}}</a>
                        </li>
                        <li><a href="#"><i class="far fa-eye" style="color: #015fc9"></i> {{newsDetail.clicksNum || 0}} 查看</a>
                        </li>
                    </ul>
                    <h3 class="news-details__title">{{newsDetail.name}}</h3>
                    <div v-html="newsDetail.content"></div>

                    <!-- 互动按钮区域 -->
                    <div class="news-interaction" style="margin-top: 30px; padding: 20px 0; border-top: 1px solid #e5e5e5;">
                        <div class="interaction-buttons" style="display: flex; justify-content: center; gap: 40px;">
                            <!-- 点赞按钮 -->
                            <div class="interaction-item" :class="{'liked': isLiked}" style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" @click="handleLike">
                                <div class="interaction-icon" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                                    <i class="fas fa-thumbs-up" style="font-size: 20px;"></i>
                                </div>
                                <span style="font-size: 14px; color: #666;">
                                    点赞<span v-if="newsDetail.likeCount && newsDetail.likeCount > 0"> {{newsDetail.likeCount}}</span>
                                </span>
                            </div>

                            <!-- 收藏按钮 -->
                            <div class="interaction-item" :class="{'favorited': isFavorited}" style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" @click="handleFavorite">
                                <div class="interaction-icon" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                                    <i class="fas fa-star" style="font-size: 20px;"></i>
                                </div>
                                <span style="font-size: 14px; color: #666;">
                                    收藏<span v-if="newsDetail.favoriteCount && newsDetail.favoriteCount > 0"> {{newsDetail.favoriteCount}}</span>
                                </span>
                            </div>

                            <!-- 分享按钮 -->
                            <div class="interaction-item" style="display: flex; flex-direction: column; align-items: center; cursor: pointer;">
                                <div class="interaction-icon" style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                                    <i class="fas fa-share-alt" style="font-size: 20px;"></i>
                                </div>
                                <span style="font-size: 14px; color: #666;">分享</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="services-one__bottom">
                    <div class="services-one__container">
                        <div class="row">
                            <div class="col-xl-3 col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="100ms"
                                v-for="item in businessPartner" :key="item">
                                <div class="services-one__single">
                                    <div class="service-one__img">
                                        <img :src="item.img" alt="">
                                    </div>
                                    <div class="service-one__content">
                                        <h2 class="service-one__title" style="height: 60px"><a target="_blank"
                                                :href="item.firmUrl">{{item.name}}</a></h2>
                                        <p class="service-one__text">成立时间：{{item.registrationYear}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </section>
            <!--News One End-->

            <!--Site Footer Start-->
            <footer class="site-footer">
                <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
                </div>
                <div class="container">
                    <div class="site-footer__top">
                        <div class="row">
<!--                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">-->
<!--                                <div class="footer-widget__column footer-widget__about">-->
<!--                                    <div class="footer-widget__logo">-->
<!--                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 34px"></a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                                <div class="footer-widget__column footer-widget__contact clearfix">
                                    <h3 class="footer-widget__title">公司地址</h3>
                                    <ul class="footer-widget__contact-list list-unstyled clearfix">
                                        <li>
                                            <div class="icon">
                                                <span class="icon-pin"></span>
                                            </div>
                                            <div class="text">
                                                <p>{{companyInfo.address}}</p>
                                            </div>
                                        </li>
                                    </ul>
                                    <div class="footer-widget__open-hour">
                                        <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                        <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                                <div class="footer-widget__column footer-widget__newsletter">
                                    <h3 class="footer-widget__title">联系电话</h3>
                                    <div class="footer-widget__phone">
                                        <div class="footer-widget__phone-icon">
                                            <span class="icon-telephone"></span>
                                        </div>
                                        <div class="footer-widget__phone-text">
                                            <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                            <p>欢迎拨打</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="site-footer__bottom">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="site-footer__bottom-inner">
                                    <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                            href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!--Site Footer End-->


        </div><!-- /.page-wrapper -->


        <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
            <div class="mobile-nav__overlay mobile-nav__toggler"></div>
            <!-- /.mobile-nav__overlay -->
            <div class="mobile-nav__content">
                <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i
                        class="fa fa-times"></i></span>

                <div class="logo-box">
                    <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                            alt=""></a>
                </div>
                <!-- /.logo-box -->
                <div class="mobile-nav__container"></div>
                <!-- /.mobile-nav__container -->



            </div>
            <!-- /.mobile-nav__content -->
        </div>
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script>
      //const jeeApi = "https://www.taiyibx.com";
      //const jeeApi = "http://*************:8080";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                companyInfo: {},
                newsDetail: {},
                keyword: '',
                isLiked: false,
                isFavorited: false,
                // 登录相关
                showLoginPopup: false,
                isLoggedIn: false,
                userInfo: null,
                loginLoading: false,
                captchaImage: '',
                loginForm: {
                    username: '',
                    password: '',
                    captcha: '',
                    checkKey: ''
                }
            },
            mounted() {
                // this.getBanner()
                this.getNews()
                this.getBusinessPartner()
                this.getRecommend()
                this.getCompanyInfo()
                this.checkLoginStatus()
                this.refreshCaptcha()
            },
            methods: {
                // 获取轮播
                async getBanner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.bannerList = data.result.records
                    this.$nextTick(() => {
                        window.thmSwiperInit();
                    })

                },
                // 获取新闻
                async getNews() {
                    // 获取当前URL
                    const url = window.location.href;

                    // 使用URLSearchParams来解析URL中的参数
                    const params = new URLSearchParams(url.split('?')[1]);

                    // 获取id参数的值
                    const id = params.get('id');
                    const response = await fetch(jeeApi + '/jeecg-boot/api/wechat/appNews/queryById?id=' + id, {
                        method: 'get',
                    })
                    const data = await response.json();
                    // this.newsList = data.result.records
                    this.newsDetail = data.result

                },
                // 合作伙伴
                async getBusinessPartner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/firmInformation/list?name=' + this.keyword, {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.businessPartner = data.result.records

                },
                // 推荐
                async getRecommend() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.recommendList = data.result.records

                },
                // 推荐
                async getCompanyInfo() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.companyInfo = data.result

                },
                // 点赞功能
                async handleLike() {
                    if (this.isLiked) {
                        // 如果已经点赞，可以选择不允许取消点赞，或者实现取消点赞逻辑
                        return;
                    }

                    try {
                        // 获取当前URL中的id参数
                        const url = window.location.href;
                        const params = new URLSearchParams(url.split('?')[1]);
                        const id = params.get('id');

                        // 检查登录状态
                        const token = localStorage.getItem('X-Access-Token');
                        const userInfo = localStorage.getItem('userInfo');
                        const isLoggedIn = !!(token && userInfo);

                        let apiUrl, headers, requestBody;

                        if (isLoggedIn) {
                            // 已登录用户调用 incrementCount 接口
                            apiUrl = jeeApi + '/jeecg-boot/wh/recordStatistics/increment';
                            headers = {
                                'Content-Type': 'application/json',
                                'X-Access-Token': token
                            };
                            requestBody = {
                                pid: id,
                                recordType: 3,  // 3表示点赞记录
                                contentType: 2  // 2表示资讯内容
                            };
                        } else {
                            // 未登录用户调用 ipIncrementCount 接口
                            apiUrl = jeeApi + '/jeecg-boot/wh/recordStatistics/ipIncrementCount';
                            headers = {
                                'Content-Type': 'application/json'
                            };
                            requestBody = {
                                pid: id,
                                recordType: 3,  // 3表示点赞记录
                                contentType: 2  // 2表示资讯内容
                            };
                        }

                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(requestBody)
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.isLiked = true;
                            // 更新点赞数量
                            this.newsDetail.likeCount = (this.newsDetail.likeCount || 0) + 1;
                            console.log('点赞成功:', data.message);
                        } else {
                            console.error('点赞失败:', data.message);
                            // 如果是未登录用户且今日已操作过，给出提示
                            if (!isLoggedIn && data.message && data.message.includes('今日已操作过')) {
                                alert('今日已点赞过，请明天再试');
                            } else {
                                alert(data.message || '点赞失败');
                            }
                        }
                    } catch (error) {
                        console.error('点赞请求失败:', error);
                        alert('点赞失败，请稍后重试');
                    }
                },
                // 收藏功能
                async handleFavorite() {
                    // 检查用户是否登录
                    if (!this.isLoggedIn) {
                        this.showLoginPopup = true;
                        return;
                    }

                    try {
                        // 获取当前URL中的id参数
                        const url = window.location.href;
                        const params = new URLSearchParams(url.split('?')[1]);
                        const id = params.get('id');

                        const token = localStorage.getItem('X-Access-Token');
                        const response = await fetch(jeeApi + '/jeecg-boot/wh/recordStatistics/increment', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Access-Token': token
                            },
                            body: JSON.stringify({
                                pid: id,
                                recordType: 2,  // 2表示收藏记录
                                contentType: 2  // 2表示资讯内容
                            })
                        });

                        const data = await response.json();
                        if (data.success) {
                            // 切换收藏状态
                            this.isFavorited = !this.isFavorited;
                            // 根据返回消息更新收藏数量
                            if (data.message.includes('收藏成功')) {
                                this.newsDetail.favoriteCount = (this.newsDetail.favoriteCount || 0) + 1;
                            } else if (data.message.includes('取消收藏成功')) {
                                this.newsDetail.favoriteCount = Math.max((this.newsDetail.favoriteCount || 0) - 1, 0);
                            }
                            console.log(data.message);
                        } else {
                            console.error('收藏操作失败:', data.message);
                        }
                    } catch (error) {
                        console.error('收藏请求失败:', error);
                    }
                },
                // 检查登录状态
                checkLoginStatus() {
                    const token = localStorage.getItem('X-Access-Token');
                    const userInfo = localStorage.getItem('userInfo');
                    if (token && userInfo) {
                        this.isLoggedIn = true;
                        this.userInfo = JSON.parse(userInfo);
                    }
                },
                // 刷新验证码
                async refreshCaptcha() {
                    try {
                        const timestamp = new Date().getTime();
                        const response = await fetch(`${jeeApi}/jeecg-boot/sys/randomImage/${timestamp}`, {
                            method: 'GET'
                        });
                        const data = await response.json();
                        if (data.success) {
                            this.captchaImage = `data:image/png;base64,${data.result.img}`;
                            this.loginForm.checkKey = data.result.checkKey;
                        }
                    } catch (error) {
                        console.error('获取验证码失败:', error);
                    }
                },
                // 关闭登录弹框
                closeLoginPopup() {
                    this.showLoginPopup = false;
                    this.resetLoginForm();
                },
                // 重置登录表单
                resetLoginForm() {
                    this.loginForm = {
                        username: '',
                        password: '',
                        captcha: '',
                        checkKey: ''
                    };
                    this.loginLoading = false;
                    this.refreshCaptcha();
                },
                // 处理登录
                async handleLogin() {
                    if (!this.loginForm.username || !this.loginForm.password || !this.loginForm.captcha) {
                        alert('请输入用户名、密码和验证码');
                        return;
                    }

                    this.loginLoading = true;

                    try {
                        const response = await fetch(`${jeeApi}/jeecg-boot/sys/login`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                username: this.loginForm.username,
                                password: this.loginForm.password,
                                captcha: this.loginForm.captcha,
                                checkKey: this.loginForm.checkKey
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // 保存登录信息
                            localStorage.setItem('X-Access-Token', data.result.token);
                            localStorage.setItem('userInfo', JSON.stringify(data.result.userInfo));

                            this.isLoggedIn = true;
                            this.userInfo = data.result.userInfo;
                            this.closeLoginPopup();

                            alert('登录成功');
                        } else {
                            alert(data.message || '登录失败');
                            this.refreshCaptcha(); // 刷新验证码
                        }
                    } catch (error) {
                        console.error('登录失败:', error);
                        alert('登录失败，请稍后重试');
                        this.refreshCaptcha(); // 刷新验证码
                    } finally {
                        this.loginLoading = false;
                    }
                }
            }
        })
    </script>

    <!-- 登录弹框 -->
    <div class="login-popup" :class="{ active: showLoginPopup }">
        <div class="search-popup__overlay" @click="closeLoginPopup"></div>
        <div class="search-popup__content">
            <div class="login-popup__header">
                <h3>用户登录</h3>
                <button type="button" class="login-popup__close" @click="closeLoginPopup">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <form @submit.prevent="handleLogin" class="login-popup__form">
                <!-- 用户名输入框 -->
                <div class="login-form-row">
                    <input type="text" v-model="loginForm.username" placeholder="请输入用户名" required>
                </div>

                <!-- 密码输入框 -->
                <div class="login-form-row">
                    <input type="password" v-model="loginForm.password" placeholder="请输入密码" required>
                </div>

                <!-- 验证码输入框和验证码图片 -->
                <div class="login-form-row">
                    <div class="login-sms-row">
                        <div class="login-sms-input">
                            <input type="text" v-model="loginForm.captcha" placeholder="请输入验证码" maxlength="4" required>
                        </div>
                        <div class="login-captcha-img" @click="refreshCaptcha" style="width: 130px; height: 55px; cursor: pointer; border: 1px solid #ddd; border-radius: 6px; overflow: hidden;">
                            <img :src="captchaImage" style="width: 100%; height: 100%; object-fit: cover;" alt="验证码" title="点击刷新验证码">
                        </div>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <div class="login-form-row">
                    <button type="submit" class="login-submit-btn" :disabled="loginLoading">
                        {{ loginLoading ? '登录中...' : '登录' }}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /.login-popup -->

</body>

</html>
