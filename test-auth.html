<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证组件测试</title>
    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/auth-components.css">
    <style>
        body {
            padding: 50px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        .test-btn {
            padding: 10px 20px;
            margin: 10px;
            background-color: #015fc9;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-btn:hover {
            background-color: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>认证组件测试页面</h1>
            <p>测试新的登录/注册/重置密码弹框功能</p>
            
            <button class="test-btn" @click="showLoginPopup = true">
                打开登录弹框
            </button>
            
            <div class="status">
                <h3>当前状态</h3>
                <p><strong>登录状态:</strong> {{ isLoggedIn ? '已登录' : '未登录' }}</p>
                <p v-if="isLoggedIn"><strong>用户信息:</strong> {{ userInfo ? userInfo.email : '无' }}</p>
                <button v-if="isLoggedIn" class="test-btn" @click="showLogoutPopup = true">
                    退出登录
                </button>
            </div>
        </div>

        <!-- 登录弹框 -->
        <login-modal
            :show="showLoginPopup"
            @close="closeLoginPopup"
            @login-success="onLoginSuccess">
        </login-modal>

        <!-- 退出登录确认弹框 -->
        <logout-modal
            :show="showLogoutPopup"
            @close="closeLogoutPopup"
            @confirm="handleLogout">
        </logout-modal>
    </div>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script src="static/js/auth-components.js"></script>
    <script>
        new Vue({
            el: '#app',
            mixins: [AuthMixin],
            data: {
                // AuthMixin 会提供认证相关的数据
            },
            methods: {
                onLoginSuccess(result) {
                    console.log('✅ 登录成功:', result);
                    // AuthMixin 已经处理了状态更新
                }
            }
        });
    </script>
</body>
</html>
