/**
 * 互动组件样式
 * 包含点赞、收藏、分享按钮的样式定义
 */

/* 互动区域基础样式 */
.news-interaction {
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #e5e5e5;
}

.interaction-buttons {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

/* 互动按钮基础样式 */
.interaction-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s ease;
    user-select: none;
}

.interaction-item:hover {
    transform: translateY(-2px);
}

.interaction-item:active {
    transform: translateY(0);
}

/* 互动图标样式 */
.interaction-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    background-color: #f8f9fa !important;
    color: #666 !important;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.interaction-icon:hover {
    background-color: #e9ecef !important;
    border-color: #dee2e6;
}

/* 激活状态样式 */
.interaction-item.liked .interaction-icon {
    background-color: #333 !important;
    color: white !important;
    border-color: #333;
}

.interaction-item.favorited .interaction-icon {
    background-color: #333 !important;
    color: white !important;
    border-color: #333;
}

/* 文字标签样式 */
.interaction-item span {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    text-align: center;
    min-height: 20px;
    display: flex;
    align-items: center;
}

.interaction-item.liked span {
    color: #333;
    font-weight: 600;
}

.interaction-item.favorited span {
    color: #333;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .interaction-buttons {
        gap: 30px;
    }
    
    .interaction-icon {
        width: 45px;
        height: 45px;
    }
    
    .interaction-item span {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .interaction-buttons {
        gap: 20px;
    }
    
    .interaction-icon {
        width: 40px;
        height: 40px;
    }
    
    .interaction-icon i {
        font-size: 18px !important;
    }
    
    .interaction-item span {
        font-size: 12px;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.interaction-item.liked .interaction-icon,
.interaction-item.favorited .interaction-icon {
    animation: pulse 0.3s ease-in-out;
}

/* 加载状态 */
.interaction-item.loading {
    pointer-events: none;
    opacity: 0.6;
}

.interaction-item.loading .interaction-icon {
    position: relative;
}

.interaction-item.loading .interaction-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 特殊主题色彩 */
.interaction-item.theme-orange.liked .interaction-icon {
    background-color: #ff6600 !important;
    border-color: #ff6600;
}

.interaction-item.theme-orange.favorited .interaction-icon {
    background-color: #ff6600 !important;
    border-color: #ff6600;
}

.interaction-item.theme-blue.liked .interaction-icon {
    background-color: #007bff !important;
    border-color: #007bff;
}

.interaction-item.theme-blue.favorited .interaction-icon {
    background-color: #007bff !important;
    border-color: #007bff;
}

/* 禁用状态 */
.interaction-item.disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
}

/* 成功提示动画 */
.interaction-success {
    position: relative;
}

.interaction-success::after {
    content: '✓';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background-color: #28a745;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: successPop 0.5s ease-in-out;
}

@keyframes successPop {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 数字徽章样式 */
.interaction-item .count-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* 工具提示样式 */
.interaction-item[title] {
    position: relative;
}

.interaction-item[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.interaction-item[title]:hover::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
}
