# 互动组件使用说明

## 概述

`InteractionComponent` 是一个通用的互动组件，包含点赞、收藏、分享功能。支持已登录和未登录用户的不同处理逻辑。

## 文件结构

- `static/js/interaction-component.js` - 组件JavaScript代码
- `static/css/interaction-component.css` - 组件样式文件

## 使用方法

### 1. 引入文件

在HTML页面中引入必要的文件：

```html
<!-- 引入样式文件 -->
<link rel="stylesheet" href="static/css/interaction-component.css">

<!-- 引入Vue.js -->
<script src="static/js/vue.js"></script>

<!-- 引入组件文件 -->
<script src="static/js/interaction-component.js"></script>
```

### 2. 注册组件

在Vue实例中注册组件：

```javascript
new Vue({
    el: '#app',
    components: {
        'interaction-component': InteractionComponent
    },
    // ... 其他配置
});
```

### 3. 使用组件

在模板中使用组件：

```html
<interaction-component 
    :content-id="contentId"
    :content-type="contentType"
    :initial-like-count="likeCount"
    :initial-favorite-count="favoriteCount"
    :initial-share-count="shareCount"
    :on-show-login="showLoginHandler"
    @like-success="onLikeSuccess"
    @favorite-success="onFavoriteSuccess"
    @share-success="onShareSuccess">
</interaction-component>
```

## 属性说明

### Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| content-id | String | 是 | - | 内容ID |
| content-type | Number | 是 | - | 内容类型：1.保险产品 2.保险资讯 3.保险公司 |
| initial-like-count | Number | 否 | 0 | 初始点赞数量 |
| initial-favorite-count | Number | 否 | 0 | 初始收藏数量 |
| initial-share-count | Number | 否 | 0 | 初始分享数量 |
| on-show-login | Function | 否 | null | 显示登录弹框的回调函数 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| like-success | {contentId, likeCount} | 点赞成功时触发 |
| favorite-success | {contentId, favoriteCount, isFavorited} | 收藏成功时触发 |
| share-success | {contentId, shareCount} | 分享成功时触发 |

## 完整示例

### HTML部分

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="static/css/interaction-component.css">
</head>
<body>
    <div id="app">
        <div class="content">
            <h1>{{contentTitle}}</h1>
            <div class="content-body">
                {{contentBody}}
            </div>
            
            <!-- 使用互动组件 -->
            <interaction-component 
                :content-id="getCurrentContentId()"
                :content-type="2"
                :initial-like-count="contentData.likeCount || 0"
                :initial-favorite-count="contentData.favoriteCount || 0"
                :initial-share-count="contentData.shareCount || 0"
                :on-show-login="showLogin"
                @like-success="onLikeSuccess"
                @favorite-success="onFavoriteSuccess"
                @share-success="onShareSuccess">
            </interaction-component>
        </div>
    </div>

    <script src="static/js/vue.js"></script>
    <script src="static/js/interaction-component.js"></script>
</body>
</html>
```

### JavaScript部分

```javascript
new Vue({
    el: '#app',
    components: {
        'interaction-component': InteractionComponent
    },
    data: {
        contentTitle: '示例内容标题',
        contentBody: '这是示例内容...',
        contentData: {
            id: '123',
            likeCount: 10,
            favoriteCount: 5,
            shareCount: 3
        }
    },
    methods: {
        // 获取当前内容ID
        getCurrentContentId() {
            // 从URL参数或其他方式获取内容ID
            const params = new URLSearchParams(window.location.search);
            return params.get('id') || this.contentData.id;
        },
        
        // 显示登录弹框
        showLogin() {
            // 实现显示登录弹框的逻辑
            console.log('显示登录弹框');
        },
        
        // 点赞成功处理
        onLikeSuccess(data) {
            console.log('点赞成功:', data);
            this.contentData.likeCount = data.likeCount;
        },
        
        // 收藏成功处理
        onFavoriteSuccess(data) {
            console.log('收藏操作成功:', data);
            this.contentData.favoriteCount = data.favoriteCount;
        },
        
        // 分享成功处理
        onShareSuccess(data) {
            console.log('分享成功:', data);
            this.contentData.shareCount = data.shareCount;
        }
    }
});
```

## API接口说明

组件会根据用户登录状态调用不同的API接口：

### 已登录用户
- 接口：`/jeecg-boot/wh/recordStatistics/increment`
- 请求头：包含 `X-Access-Token`
- 支持点赞/收藏的切换操作

### 未登录用户
- 点赞接口：`/jeecg-boot/wh/recordStatistics/ipIncrementCount`
- 收藏：需要登录才能操作
- 基于IP限制，每天每个IP只能操作一次

## 样式自定义

可以通过CSS覆盖默认样式来自定义外观：

```css
/* 自定义主题色 */
.interaction-item.theme-custom.liked .interaction-icon {
    background-color: #your-color !important;
    border-color: #your-color;
}

/* 自定义按钮大小 */
.interaction-icon {
    width: 60px !important;
    height: 60px !important;
}
```

## 注意事项

1. 确保页面已引入Vue.js
2. 确保API接口地址正确配置（jeeApi变量）
3. 登录状态检查依赖localStorage中的token和userInfo
4. 分享功能在不同浏览器中表现可能不同
5. 组件会自动处理错误情况并给出用户提示
