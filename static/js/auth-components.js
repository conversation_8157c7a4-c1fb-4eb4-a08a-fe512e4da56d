/**
 * 认证组件管理器
 * 统一管理登录弹框、用户头像、下拉菜单等功能
 */

// 用户头像组件
Vue.component('user-avatar', {
    template: `
        <div class="main-menu__right">
            <div class="main-menu__user-container" style="position: relative; display: inline-block;">
                <a href="#" class="main-menu__user user-toggler" :class="{ 'logged-in': isLoggedIn }" @click.prevent="handleUserClick" title="用户中心">
                    <i class="fa fa-user-circle"></i>
                </a>
                <!-- 用户菜单弹框 -->
                <div class="user-menu-popup main-header-user-menu" :class="{ active: showUserMenu }" v-if="isLoggedIn" id="main-user-menu">
                    <div class="user-menu-popup__item">
                        <a href="news-sidebar.html" class="user-menu-popup__link">
                            <i class="fa fa-user"></i>
                            <span>个人中心</span>
                        </a>
                    </div>
                    <div class="user-menu-popup__item">
                        <a href="#" @click.prevent="showLogoutConfirm" class="user-menu-popup__link">
                            <i class="fa fa-sign-out-alt"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `,
    props: {
        isLoggedIn: {
            type: Boolean,
            default: false
        },
        showUserMenu: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        handleUserClick(event) {
            this.$emit('user-click', event);
        },
        showLogoutConfirm() {
            this.$emit('logout-confirm');
        }
    }
});

// 登录弹框组件
Vue.component('login-modal', {
    template: `
        <div class="login-popup" :class="{ active: show }">
            <div class="search-popup__overlay" @click="closeLogin"></div>
            <div class="search-popup__content">
                <!-- 重置密码模式 -->
                <div v-if="currentMode === 'reset'" class="reset-password-container">
                    <div class="login-popup__header">
                        <h3>🔁 重置密码</h3>
                        <button type="button" class="login-popup__close" @click="closeLogin">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="reset-password__description">
                        <p>请输入用户名获取验证码，验证身份后即可设置新密码</p>
                    </div>
                    <form @submit.prevent="handleResetPassword" class="login-popup__form">
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-user"></i>
                                <input v-model="resetForm.username" type="text" placeholder="用户名" required>
                            </div>
                        </div>
                        <div class="login-popup__form-group">
                            <button type="button" @click="sendResetCode" class="send-code-btn" :disabled="resetCodeSending">
                                {{ resetCodeSending ? '发送中...' : '发送验证码' }}
                            </button>
                        </div>
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-key"></i>
                                <input v-model="resetForm.code" type="text" placeholder="验证码" required>
                            </div>
                        </div>
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-lock"></i>
                                <input v-model="resetForm.newPassword" type="password" placeholder="新密码" required>
                            </div>
                        </div>
                        <button type="submit" class="thm-btn login-popup__submit" :disabled="resetLoading">
                            {{ resetLoading ? '重置中...' : '确认重置密码' }}
                        </button>
                        <div class="back-to-login">
                            <a href="#" @click.prevent="switchMode('login')">← 返回登录</a>
                        </div>
                    </form>
                </div>

                <!-- 登录/注册模式 -->
                <div v-else class="auth-container">
                    <div class="login-popup__header">
                        <div class="auth-tabs">
                            <button type="button"
                                    class="auth-tab"
                                    :class="{ active: currentMode === 'login' }"
                                    @click="switchMode('login')">
                                登录
                            </button>
                            <button type="button"
                                    class="auth-tab"
                                    :class="{ active: currentMode === 'register' }"
                                    @click="switchMode('register')">
                                注册
                            </button>
                        </div>
                        <button type="button" class="login-popup__close" @click="closeLogin">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>

                    <!-- 登录表单 -->
                    <form v-if="currentMode === 'login'" @submit.prevent="handleLogin" class="login-popup__form">
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-user"></i>
                                <input v-model="loginForm.username" type="text" placeholder="用户名" required>
                            </div>
                        </div>
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-lock"></i>
                                <input v-model="loginForm.password" type="password" placeholder="密码" required>
                            </div>
                        </div>
                        <div class="login-popup__form-group remember-me">
                            <label class="checkbox-container">
                                <input type="checkbox" v-model="loginForm.rememberMe">
                                <span class="checkmark"></span>
                                记住我
                            </label>
                        </div>
                        <button type="submit" class="thm-btn login-popup__submit" :disabled="loginLoading">
                            {{ loginLoading ? '登录中...' : '登录' }}
                        </button>
                        <div class="forgot-password">
                            <a href="#" @click.prevent="switchMode('reset')">忘记密码？ →</a>
                        </div>
                    </form>

                    <!-- 注册表单 -->
                    <form v-if="currentMode === 'register'" @submit.prevent="handleRegister" class="login-popup__form">
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-user"></i>
                                <input v-model="registerForm.username" type="text" placeholder="用户名" required>
                            </div>
                        </div>
                        <div class="login-popup__form-group">
                            <div class="input-with-icon">
                                <i class="fa fa-lock"></i>
                                <input v-model="registerForm.password" type="password" placeholder="密码" required>
                            </div>
                        </div>
                        <button type="submit" class="thm-btn login-popup__submit" :disabled="registerLoading">
                            {{ registerLoading ? '注册中...' : '注册' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    `,
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            currentMode: 'login', // 'login', 'register', 'reset'
            loginForm: {
                username: '',
                password: '',
                rememberMe: false
            },
            registerForm: {
                username: '',
                password: ''
            },
            resetForm: {
                username: '',
                code: '',
                newPassword: ''
            },
            loginLoading: false,
            registerLoading: false,
            resetLoading: false,
            resetCodeSending: false
        };
    },
    watch: {
        show(newVal) {
            // 当弹框显示时重置到登录模式
            if (newVal) {
                this.currentMode = 'login';
                this.resetAllForms();
            }
        }
    },
    methods: {
        closeLogin() {
            this.$emit('close');
            this.resetAllForms();
        },

        switchMode(mode) {
            this.currentMode = mode;
            this.resetAllForms();
        },

        resetAllForms() {
            this.loginForm = {
                username: '',
                password: '',
                rememberMe: false
            };
            this.registerForm = {
                username: '',
                password: ''
            };
            this.resetForm = {
                username: '',
                code: '',
                newPassword: ''
            };
            this.loginLoading = false;
            this.registerLoading = false;
            this.resetLoading = false;
            this.resetCodeSending = false;
        },
        


        // 发送重置密码验证码
        async sendResetCode() {
            if (!this.resetForm.username) {
                alert('请输入用户名');
                return;
            }

            this.resetCodeSending = true;
            try {
                const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/sendResetCode`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: this.resetForm.username
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert(data.message || '验证码已生成');
                } else {
                    alert(data.message || '发送验证码失败');
                }
            } catch (error) {
                console.error('发送验证码失败:', error);
                alert('发送验证码失败，请稍后重试');
            } finally {
                this.resetCodeSending = false;
            }
        },
        
        // 处理登录
        async handleLogin() {
            if (!this.loginForm.username || !this.loginForm.password) {
                alert('请输入用户名和密码');
                return;
            }

            this.loginLoading = true;
            try {
                const response = await fetch(`${jeeApi}/jeecg-boot/sys/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: this.loginForm.username,
                        password: this.loginForm.password
                    })
                });

                const data = await response.json();
                if (data.success) {
                    localStorage.setItem('X-Access-Token', data.result.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.result.userInfo));

                    this.$emit('login-success', data.result);
                    this.closeLogin();
                    alert('登录成功');
                } else {
                    alert(data.message || '登录失败');
                }
            } catch (error) {
                console.error('登录失败:', error);
                alert('登录失败，请稍后重试');
            } finally {
                this.loginLoading = false;
            }
        },

        // 处理注册
        async handleRegister() {
            if (!this.registerForm.username || !this.registerForm.password) {
                alert('请填写用户名和密码');
                return;
            }

            this.registerLoading = true;
            try {
                const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: this.registerForm.username,
                        password: this.registerForm.password
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert('注册成功，请登录');
                    this.switchMode('login');
                } else {
                    alert(data.message || '注册失败');
                }
            } catch (error) {
                console.error('注册失败:', error);
                alert('注册失败，请稍后重试');
            } finally {
                this.registerLoading = false;
            }
        },

        // 处理重置密码
        async handleResetPassword() {
            if (!this.resetForm.username || !this.resetForm.code || !this.resetForm.newPassword) {
                alert('请填写完整的重置信息');
                return;
            }

            this.resetLoading = true;
            try {
                const response = await fetch(`${jeeApi}/jeecg-boot/sys/user/resetPassword`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: this.resetForm.username,
                        code: this.resetForm.code,
                        newPassword: this.resetForm.newPassword
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert('密码重置成功，请使用新密码登录');
                    this.switchMode('login');
                } else {
                    alert(data.message || '重置密码失败');
                }
            } catch (error) {
                console.error('重置密码失败:', error);
                alert('重置密码失败，请稍后重试');
            } finally {
                this.resetLoading = false;
            }
        }
    }
});

// 退出确认弹框组件
Vue.component('logout-modal', {
    template: `
        <div class="logout-confirm-popup" :class="{ active: show }">
            <div class="search-popup__overlay" @click="closeLogout"></div>
            <div class="search-popup__content" style="max-width: 400px;">
                <div class="logout-confirm__content">
                    <h3>退出登录</h3>
                    <p>您好！确认要退出当前登录吗？</p>
                    <div class="logout-confirm__buttons">
                        <button type="button" @click="closeLogout" class="thm-btn logout-confirm__btn logout-confirm__btn--cancel">取消</button>
                        <button type="button" @click="confirmLogout" class="thm-btn logout-confirm__btn logout-confirm__btn--confirm">确认退出</button>
                    </div>
                </div>
            </div>
        </div>
    `,
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        closeLogout() {
            this.$emit('close');
        },
        
        confirmLogout() {
            this.$emit('confirm');
        }
    }
});

// 认证管理器 Mixin
const AuthMixin = {
    data() {
        return {
            // 登录相关数据
            isLoggedIn: false,
            userInfo: null,
            showLoginPopup: false,
            showUserMenu: false,
            showLogoutPopup: false
        };
    },
    
    mounted() {
        this.checkLoginStatus();
    },
    
    beforeDestroy() {
        // 清理事件监听器
        document.removeEventListener('click', this.closeUserMenu, true);
    },
    
    methods: {
        // 检查登录状态
        checkLoginStatus() {
            const token = localStorage.getItem('X-Access-Token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                this.isLoggedIn = true;
                this.userInfo = JSON.parse(userInfo);
            } else {
                this.isLoggedIn = false;
                this.userInfo = null;
            }
        },
        
        // 获取认证头
        getAuthHeaders() {
            const token = localStorage.getItem('X-Access-Token');
            return {
                'Content-Type': 'application/json',
                'X-Access-Token': token || ''
            };
        },
        
        // 处理用户头像点击
        handleUserClick(event) {
            event.stopPropagation();
            
            if (this.isLoggedIn) {
                this.showUserMenu = !this.showUserMenu;
                
                // 如果菜单打开，添加全局点击监听器
                if (this.showUserMenu) {
                    this.$nextTick(() => {
                        document.addEventListener('click', this.closeUserMenu, true);
                    });
                } else {
                    document.removeEventListener('click', this.closeUserMenu, true);
                }
            } else {
                this.showLoginPopup = true;
            }
        },
        
        // 关闭用户菜单
        closeUserMenu(event) {
            // 检查点击是否在用户菜单区域内
            const userContainer = event.target.closest('.main-menu__user-container');
            
            if (!userContainer) {
                this.showUserMenu = false;
                document.removeEventListener('click', this.closeUserMenu, true);
            }
        },
        
        // 显示退出确认
        showLogoutConfirm() {
            this.showUserMenu = false;
            this.showLogoutPopup = true;
            // 移除事件监听器
            document.removeEventListener('click', this.closeUserMenu, true);
        },
        
        // 关闭登录弹框
        closeLoginPopup() {
            this.showLoginPopup = false;
        },
        
        // 关闭退出弹框
        closeLogoutPopup() {
            this.showLogoutPopup = false;
        },
        
        // 登录成功处理
        onLoginSuccess(result) {
            this.isLoggedIn = true;
            this.userInfo = result.userInfo;
            this.showLoginPopup = false;
        },
        
        // 处理退出登录
        async handleLogout() {
            try {
                const token = localStorage.getItem('X-Access-Token');
                if (token) {
                    await fetch(`${jeeApi}/jeecg-boot/sys/logout`, {
                        method: 'POST',
                        headers: {
                            'X-Access-Token': token
                        }
                    });
                }
            } catch (error) {
                console.error('退出登录请求失败:', error);
            } finally {
                // 清除本地存储
                localStorage.removeItem('X-Access-Token');
                localStorage.removeItem('userInfo');
                
                // 更新状态
                this.isLoggedIn = false;
                this.userInfo = null;
                this.showLogoutPopup = false;
                
                alert('已退出登录');
                
                // 可选：刷新页面或重定向
                // window.location.reload();
            }
        }
    }
};

// 导出给全局使用
window.AuthMixin = AuthMixin;
