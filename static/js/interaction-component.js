/**
 * 互动组件 - 包含点赞、收藏、分享功能
 * 支持已登录和未登录用户的不同处理逻辑
 */

// 互动组件的Vue组件定义
const InteractionComponent = {
    template: `
        <div class="news-interaction" style="margin-top: 30px; padding: 20px 0; border-top: 1px solid #e5e5e5;">
            <div class="interaction-buttons" style="display: flex; justify-content: center; gap: 40px;">
                <!-- 点赞按钮 -->
                <div class="interaction-item" 
                     :class="{'liked': isLiked}" 
                     style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" 
                     @click="handleLike">
                    <div class="interaction-icon" 
                         style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                        <i class="fas fa-thumbs-up" style="font-size: 20px;"></i>
                    </div>
                    <span style="font-size: 14px; color: #666;">
                        点赞<span v-if="likeCount && likeCount > 0"> {{likeCount}}</span>
                    </span>
                </div>

                <!-- 收藏按钮 -->
                <div class="interaction-item" 
                     :class="{'favorited': isFavorited}" 
                     style="display: flex; flex-direction: column; align-items: center; cursor: pointer;" 
                     @click="handleFavorite">
                    <div class="interaction-icon" 
                         style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                        <i class="fas fa-star" style="font-size: 20px;"></i>
                    </div>
                    <span style="font-size: 14px; color: #666;">
                        收藏<span v-if="favoriteCount && favoriteCount > 0"> {{favoriteCount}}</span>
                    </span>
                </div>

                <!-- 分享按钮 -->
                <div class="interaction-item" 
                     style="display: flex; flex-direction: column; align-items: center; cursor: pointer;"
                     @click="handleShare">
                    <div class="interaction-icon" 
                         style="width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 8px;">
                        <i class="fas fa-share-alt" style="font-size: 20px;"></i>
                    </div>
                    <span style="font-size: 14px; color: #666;">分享</span>
                </div>
            </div>
        </div>
    `,
    props: {
        // 内容ID
        contentId: {
            type: String,
            required: true
        },
        // 内容类型：1.保险产品 2.保险资讯 3.保险公司
        contentType: {
            type: Number,
            required: true
        },
        // 初始点赞数量
        initialLikeCount: {
            type: Number,
            default: 0
        },
        // 初始收藏数量
        initialFavoriteCount: {
            type: Number,
            default: 0
        },
        // 初始分享数量
        initialShareCount: {
            type: Number,
            default: 0
        },
        // 是否显示登录弹框的回调
        onShowLogin: {
            type: Function,
            default: null
        }
    },
    data() {
        return {
            isLiked: false,
            isFavorited: false,
            likeCount: this.initialLikeCount,
            favoriteCount: this.initialFavoriteCount,
            shareCount: this.initialShareCount
        };
    },
    methods: {
        // 检查用户是否已登录
        isUserLoggedIn() {
            const token = localStorage.getItem('X-Access-Token');
            const userInfo = localStorage.getItem('userInfo');
            return !!(token && userInfo);
        },

        // 获取认证头
        getAuthHeaders() {
            const token = localStorage.getItem('X-Access-Token');
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['X-Access-Token'] = token;
            }
            return headers;
        },

        // 点赞功能
        async handleLike() {
            if (this.isLiked) {
                // 如果已经点赞，可以选择不允许取消点赞，或者实现取消点赞逻辑
                return;
            }

            try {
                const isLoggedIn = this.isUserLoggedIn();
                let apiUrl, headers, requestBody;

                if (isLoggedIn) {
                    // 已登录用户调用 increment 接口
                    apiUrl = jeeApi + '/jeecg-boot/wh/recordStatistics/increment';
                    headers = this.getAuthHeaders();
                    requestBody = {
                        pid: this.contentId,
                        recordType: 3,  // 3表示点赞记录
                        contentType: this.contentType
                    };
                } else {
                    // 未登录用户调用 ipIncrementCount 接口
                    apiUrl = jeeApi + '/jeecg-boot/wh/recordStatistics/ipIncrementCount';
                    headers = {
                        'Content-Type': 'application/json'
                    };
                    requestBody = {
                        pid: this.contentId,
                        recordType: 3,  // 3表示点赞记录
                        contentType: this.contentType
                    };
                }

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                if (data.success) {
                    this.isLiked = true;
                    // 更新点赞数量
                    this.likeCount = (this.likeCount || 0) + 1;
                    console.log('点赞成功:', data.message);
                    
                    // 触发父组件事件
                    this.$emit('like-success', {
                        contentId: this.contentId,
                        likeCount: this.likeCount
                    });
                } else {
                    console.error('点赞失败:', data.message);
                    // 如果是未登录用户且今日已操作过，给出提示
                    if (!isLoggedIn && data.message && data.message.includes('今日已操作过')) {
                        alert('今日已点赞过，请明天再试');
                    } else {
                        alert(data.message || '点赞失败');
                    }
                }
            } catch (error) {
                console.error('点赞请求失败:', error);
                alert('点赞失败，请稍后重试');
            }
        },

        // 收藏功能
        async handleFavorite() {
            // 检查用户是否登录
            if (!this.isUserLoggedIn()) {
                if (this.onShowLogin) {
                    this.onShowLogin();
                } else {
                    alert('请先登录');
                }
                return;
            }

            try {
                const headers = this.getAuthHeaders();
                const response = await fetch(jeeApi + '/jeecg-boot/wh/recordStatistics/increment', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        pid: this.contentId,
                        recordType: 2,  // 2表示收藏记录
                        contentType: this.contentType
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // 切换收藏状态
                    this.isFavorited = !this.isFavorited;
                    // 根据返回消息更新收藏数量
                    if (data.message.includes('收藏成功')) {
                        this.favoriteCount = (this.favoriteCount || 0) + 1;
                    } else if (data.message.includes('取消收藏成功')) {
                        this.favoriteCount = Math.max((this.favoriteCount || 0) - 1, 0);
                    }
                    console.log(data.message);
                    
                    // 触发父组件事件
                    this.$emit('favorite-success', {
                        contentId: this.contentId,
                        favoriteCount: this.favoriteCount,
                        isFavorited: this.isFavorited
                    });
                } else {
                    console.error('收藏操作失败:', data.message);
                    alert(data.message || '收藏操作失败');
                }
            } catch (error) {
                console.error('收藏请求失败:', error);
                alert('收藏操作失败，请稍后重试');
            }
        },

        // 分享功能
        async handleShare() {
            try {
                // 获取当前页面URL
                const currentUrl = window.location.href;
                
                // 尝试使用Web Share API（移动端支持较好）
                if (navigator.share) {
                    await navigator.share({
                        title: document.title,
                        url: currentUrl
                    });
                    
                    // 分享成功后增加分享统计
                    await this.incrementShareCount();
                } else {
                    // 降级方案：复制链接到剪贴板
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(currentUrl);
                        alert('链接已复制到剪贴板');
                        
                        // 复制成功后增加分享统计
                        await this.incrementShareCount();
                    } else {
                        // 最后的降级方案：显示链接让用户手动复制
                        const textArea = document.createElement('textarea');
                        textArea.value = currentUrl;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        alert('链接已复制到剪贴板');
                        
                        // 复制成功后增加分享统计
                        await this.incrementShareCount();
                    }
                }
            } catch (error) {
                console.error('分享失败:', error);
                // 如果分享失败，仍然尝试复制链接
                try {
                    const currentUrl = window.location.href;
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(currentUrl);
                        alert('链接已复制到剪贴板');
                        await this.incrementShareCount();
                    }
                } catch (copyError) {
                    console.error('复制链接失败:', copyError);
                    alert('分享失败，请稍后重试');
                }
            }
        },

        // 增加分享统计
        async incrementShareCount() {
            try {
                const isLoggedIn = this.isUserLoggedIn();
                let apiUrl, headers;

                if (isLoggedIn) {
                    apiUrl = jeeApi + '/jeecg-boot/wh/recordStatistics/increment';
                    headers = this.getAuthHeaders();
                } else {
                    apiUrl = jeeApi + '/jeecg-boot/wh/recordStatistics/ipIncrementCount';
                    headers = {
                        'Content-Type': 'application/json'
                    };
                }

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        pid: this.contentId,
                        recordType: 4,  // 4表示分享记录
                        contentType: this.contentType
                    })
                });

                const data = await response.json();
                if (data.success) {
                    this.shareCount = (this.shareCount || 0) + 1;
                    
                    // 触发父组件事件
                    this.$emit('share-success', {
                        contentId: this.contentId,
                        shareCount: this.shareCount
                    });
                }
            } catch (error) {
                console.error('分享统计失败:', error);
                // 分享统计失败不影响用户体验，只记录错误
            }
        }
    }
};

// 导出组件供全局使用
window.InteractionComponent = InteractionComponent;
